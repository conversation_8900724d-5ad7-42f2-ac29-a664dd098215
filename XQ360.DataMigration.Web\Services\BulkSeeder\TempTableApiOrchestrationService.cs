using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Enhanced API orchestration service that uses temporary tables for staging before API calls
/// Combines the benefits of temporary table validation with API orchestration
/// </summary>
public class TempTableApiOrchestrationService : IApiOrchestrationService
{
    private readonly ILogger<TempTableApiOrchestrationService> _logger;
    private readonly ITempStagingService _tempStagingService;
    private readonly IApiOrchestrationService _apiOrchestrationService;
    private readonly BulkSeederConfiguration _config;
    private readonly IEnvironmentConfigurationService _environmentService;

    public TempTableApiOrchestrationService(
        ILogger<TempTableApiOrchestrationService> logger,
        ITempStagingService tempStagingService,
        IApiOrchestrationService apiOrchestrationService,
        IOptions<BulkSeederConfiguration> config,
        IEnvironmentConfigurationService environmentService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tempStagingService = tempStagingService ?? throw new ArgumentNullException(nameof(tempStagingService));
        _apiOrchestrationService = apiOrchestrationService ?? throw new ArgumentNullException(nameof(apiOrchestrationService));
        _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
    }

    public async Task<ApiOrchestrationResult> CreatePersonDriverBatchAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var requests = personRequests.ToList();

        _logger.LogInformation("Starting temp table enhanced Person/Driver batch creation: {TotalRequests} requests, Session: {SessionId}", 
            requests.Count, sessionId);

        if (_config.UseTempTables)
        {
            return await CreatePersonDriverWithTempTablesAsync(requests, batchSize, sessionId, cancellationToken);
        }
        else
        {
            // Fallback to direct API orchestration
            return await _apiOrchestrationService.CreatePersonDriverBatchAsync(requests, batchSize, cancellationToken);
        }
    }

    private async Task<ApiOrchestrationResult> CreatePersonDriverWithTempTablesAsync(
        List<PersonCreateRequest> requests,
        int batchSize,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        var result = new ApiOrchestrationResult
        {
            TotalRequests = requests.Count
        };

        // Use a single connection for the entire temp table session
        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Step 1: Create temporary tables
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);
            _logger.LogDebug("Created temporary staging tables for session {SessionId}", sessionId);

            // Step 2: Bulk insert to temp tables using SqlBulkCopy
            await _tempStagingService.PopulatePersonDriverTempDataAsync(connection, sessionId, requests, cancellationToken);
            _logger.LogDebug("Populated temporary tables with {RequestCount} requests", requests.Count);

            // Step 3: Validate data in temp tables
            var validationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);
            _logger.LogInformation("Validation completed: {ValidCount}/{TotalCount} valid records", 
                validationResult.ValidPersonRows, validationResult.TotalPersonRows);

            if (!validationResult.Success)
            {
                // Return validation errors
                result.Errors.AddRange(validationResult.ValidationErrors);
                result.FailedRequests = validationResult.InvalidPersonRows;
                result.SuccessfulRequests = 0;
                return result;
            }

            // Step 4: Process valid records through API in batches
            var validRequests = await GetValidatedRequestsFromTempTableAsync(connection, sessionId, cancellationToken);
            var apiResult = await _apiOrchestrationService.CreatePersonDriverBatchAsync(validRequests, batchSize, cancellationToken);

            // Step 5: Update temp table with API results (for audit trail)
            await UpdateTempTableWithApiResultsAsync(connection, sessionId, apiResult, cancellationToken);

            // Step 6: Get final processing summary
            var processingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);

            // Combine results
            result.SuccessfulRequests = apiResult.SuccessfulRequests;
            result.FailedRequests = apiResult.FailedRequests;
            result.Results = apiResult.Results;
            result.Errors.AddRange(apiResult.Errors);
            result.Success = apiResult.Success;

            _logger.LogInformation("Temp table enhanced processing completed: {Successful}/{Total} successful", 
                result.SuccessfulRequests, result.TotalRequests);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Temp table enhanced processing failed for session {SessionId}", sessionId);
            result.Errors.Add($"Processing failed: {ex.Message}");
            result.Success = false;
            return result;
        }
        finally
        {
            // Cleanup is automatic when connection closes, but we can explicitly mark session as complete
            try
            {
                await _tempStagingService.CleanupTempTablesAsync(connection, sessionId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Cleanup warning for session {SessionId}", sessionId);
            }
        }
    }

    private async Task<List<PersonCreateRequest>> GetValidatedRequestsFromTempTableAsync(
        SqlConnection connection,
        Guid sessionId,
        CancellationToken cancellationToken)
    {
        const string sql = @"
            SELECT 
                FirstName, LastName, SiteId, DepartmentId, CustomerId,
                IsDriver, IsSupervisor, WebsiteAccess,
                VORActivateDeactivate, NormalDriverAccess, CanUnlockVehicle
            FROM #PersonDriverImport 
            WHERE ImportSessionId = @SessionId 
              AND ValidationStatus = 'Valid'
            ORDER BY RowNumber
        ";

        var validatedRequests = new List<PersonCreateRequest>();

        using var command = new SqlCommand(sql, connection);
        command.Parameters.AddWithValue("@SessionId", sessionId);
        command.CommandTimeout = _config.CommandTimeout;

        using var reader = await command.ExecuteReaderAsync(cancellationToken);
        while (await reader.ReadAsync())
        {
            validatedRequests.Add(new PersonCreateRequest
            {
                FirstName = reader["FirstName"].ToString()!,
                LastName = reader["LastName"].ToString()!,
                SiteId = (Guid)reader["SiteId"],
                DepartmentId = (Guid)reader["DepartmentId"],
                CustomerId = (Guid)reader["CustomerId"],
                IsDriver = (bool)reader["IsDriver"],
                IsSupervisor = (bool)reader["IsSupervisor"],
                WebsiteAccess = (bool)reader["WebsiteAccess"],
                // Removed SendDenyMessage - not needed in data seeder, default will be used
                VORActivateDeactivate = (bool)reader["VORActivateDeactivate"],
                NormalDriverAccess = (bool)reader["NormalDriverAccess"],
                CanUnlockVehicle = (bool)reader["CanUnlockVehicle"]
            });
        }

        _logger.LogDebug("Retrieved {ValidatedCount} validated requests from temp table", validatedRequests.Count);
        return validatedRequests;
    }

    private async Task UpdateTempTableWithApiResultsAsync(
        SqlConnection connection,
        Guid sessionId,
        ApiOrchestrationResult apiResult,
        CancellationToken cancellationToken)
    {
        // Update temp table with API results for audit trail
        foreach (var kvp in apiResult.Results)
        {
            var key = kvp.Key;
            var result = kvp.Value;

            // Parse the key to get FirstName_LastName_SiteId
            var keyParts = key.Split('_');
            if (keyParts.Length >= 3)
            {
                var firstName = keyParts[0];
                var lastName = keyParts[1];
                var siteIdString = keyParts[2];

                if (Guid.TryParse(siteIdString, out var siteId))
                {
                    const string updateSql = @"
                        UPDATE #PersonDriverImport 
                        SET PersonId = @PersonId,
                            DriverId = @DriverId,
                            ProcessingAction = @ProcessingAction,
                            ProcessedAt = GETUTCDATE(),
                            ProcessingResult = @ProcessingResult,
                            ProcessingErrors = @ProcessingErrors
                        WHERE ImportSessionId = @SessionId 
                          AND FirstName = @FirstName 
                          AND LastName = @LastName
                          AND SiteId = @SiteId
                    ";

                    using var updateCommand = new SqlCommand(updateSql, connection);
                    updateCommand.Parameters.AddWithValue("@SessionId", sessionId);
                    updateCommand.Parameters.AddWithValue("@FirstName", firstName);
                    updateCommand.Parameters.AddWithValue("@LastName", lastName);
                    updateCommand.Parameters.AddWithValue("@SiteId", siteId);
                    updateCommand.Parameters.AddWithValue("@PersonId", result.PersonId ?? (object)DBNull.Value);
                    updateCommand.Parameters.AddWithValue("@DriverId", result.DriverId ?? (object)DBNull.Value);
                    updateCommand.Parameters.AddWithValue("@ProcessingAction", result.Success ? "API_Success" : "API_Failed");
                    updateCommand.Parameters.AddWithValue("@ProcessingResult", result.Success ? "Created via API" : "API call failed");
                    updateCommand.Parameters.AddWithValue("@ProcessingErrors", result.ErrorMessage ?? (object)DBNull.Value);

                    try
                    {
                        await updateCommand.ExecuteNonQueryAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to update temp table with API result for {Key}", key);
                    }
                }
            }
        }

        _logger.LogDebug("Updated temp table with {ResultCount} API results", apiResult.Results.Count);
    }

    public async Task<bool> ValidateApiConnectivityAsync()
    {
        return await _apiOrchestrationService.ValidateApiConnectivityAsync();
    }

    public async Task<bool> RefreshAuthenticationAsync()
    {
        return await _apiOrchestrationService.RefreshAuthenticationAsync();
    }

    /// <summary>
    /// Extended method for bulk operations with full temp table validation and processing
    /// </summary>
    public async Task<TempTableOrchestrationResult> CreatePersonDriverBatchWithFullValidationAsync(
        IEnumerable<PersonCreateRequest> personRequests,
        int batchSize = 100,
        bool dryRun = false,
        CancellationToken cancellationToken = default)
    {
        var sessionId = Guid.NewGuid();
        var requests = personRequests.ToList();

        _logger.LogInformation("Starting full temp table validation for {TotalRequests} requests, Session: {SessionId}, DryRun: {DryRun}",
            requests.Count, sessionId, dryRun);

        var result = new TempTableOrchestrationResult
        {
            SessionId = sessionId,
            TotalRequests = requests.Count,
            DryRun = dryRun
        };

        using var connection = new SqlConnection(_environmentService.CurrentMigrationConfiguration.DatabaseConnection);
        await connection.OpenAsync(cancellationToken);

        try
        {
            // Step 1: Create temporary tables and populate
            await _tempStagingService.CreateTempStagingTablesAsync(connection, sessionId, cancellationToken);
            await _tempStagingService.PopulatePersonDriverTempDataAsync(connection, sessionId, requests, cancellationToken);

            // Step 2: Validate
            result.ValidationResult = await _tempStagingService.ValidateTempDataAsync(connection, sessionId, cancellationToken);

            if (!result.ValidationResult.Success)
            {
                result.Success = false;
                result.Summary = $"Validation failed: {result.ValidationResult.InvalidPersonRows} invalid records";
                return result;
            }

            // Step 3: Process (API calls or dry run)
            if (dryRun)
            {
                result.ProcessingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);
                result.Success = true;
                result.Summary = $"Dry run completed: {result.ValidationResult.ValidPersonRows} records would be processed";
            }
            else
            {
                var validRequests = await GetValidatedRequestsFromTempTableAsync(connection, sessionId, cancellationToken);
                result.ApiResult = await _apiOrchestrationService.CreatePersonDriverBatchAsync(validRequests, batchSize, cancellationToken);
                await UpdateTempTableWithApiResultsAsync(connection, sessionId, result.ApiResult, cancellationToken);
                result.ProcessingResult = await _tempStagingService.MergeTempToProductionAsync(connection, sessionId, true, cancellationToken);

                result.Success = result.ApiResult.Success;
                result.Summary = $"Processing completed: {result.ApiResult.SuccessfulRequests}/{result.TotalRequests} successful";
            }

            // Step 4: Get final summary
            result.StagingSummary = await _tempStagingService.GetTempStagingSummaryAsync(connection, sessionId, cancellationToken);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Full temp table validation failed for session {SessionId}", sessionId);
            result.Success = false;
            result.Summary = $"Processing failed: {ex.Message}";
            return result;
        }
    }
}

/// <summary>
/// Extended result that includes temp table validation and processing details
/// </summary>
public class TempTableOrchestrationResult
{
    public Guid SessionId { get; set; }
    public int TotalRequests { get; set; }
    public bool DryRun { get; set; }
    public bool Success { get; set; }
    public string Summary { get; set; } = string.Empty;
    public TempValidationResult? ValidationResult { get; set; }
    public TempProcessingResult? ProcessingResult { get; set; }
    public ApiOrchestrationResult? ApiResult { get; set; }
    public TempStagingSummary? StagingSummary { get; set; }
}
