using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.SignalR;
using Polly;
using XQ360.DataMigration.Web.Models;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Web.Services.BulkSeeder;

/// <summary>
/// Main service for bulk seeding operations
/// Integrates with existing migration infrastructure while providing new seeding functionality
/// </summary>
public class BulkSeederService : IBulkSeederService
{
    private readonly ILogger<BulkSeederService> _logger;
    private readonly BulkSeederConfiguration _options;
    private readonly ISqlDataGenerationService _sqlDataService;
    private readonly IEnvironmentConfigurationService _environmentService;
    private readonly IHubContext<MigrationHub> _hubContext;

    // Phase 2: Migration Pattern Integration Services
    private readonly IApiOrchestrationService _apiOrchestrationService;
    private readonly IComplexEntityCreationService _complexEntityService;

    public BulkSeederService(
        ILogger<BulkSeederService> logger,
        IOptions<BulkSeederConfiguration> options,
        ISqlDataGenerationService sqlDataService,
        IEnvironmentConfigurationService environmentService,
        IHubContext<MigrationHub> hubContext,
        IApiOrchestrationService apiOrchestrationService,
        IComplexEntityCreationService complexEntityService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _sqlDataService = sqlDataService ?? throw new ArgumentNullException(nameof(sqlDataService));
        _environmentService = environmentService ?? throw new ArgumentNullException(nameof(environmentService));
        _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));
        _apiOrchestrationService = apiOrchestrationService ?? throw new ArgumentNullException(nameof(apiOrchestrationService));
        _complexEntityService = complexEntityService ?? throw new ArgumentNullException(nameof(complexEntityService));
    }

    /// <summary>
    /// Executes the bulk seeding operation with integration to existing migration infrastructure
    /// </summary>
    public async Task<SeederResult> ExecuteSeederAsync(SeederOptions options, CancellationToken cancellationToken = default)
    {
        return await ExecuteSeederAsync(options, null, cancellationToken);
    }

    /// <summary>
    /// Executes the bulk seeding operation with integration to existing migration infrastructure
    /// </summary>
    /// <param name="options">Seeding options</param>
    /// <param name="sessionId">Optional session ID for SignalR group messaging. If null, a new session ID will be generated.</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task<SeederResult> ExecuteSeederAsync(SeederOptions options, string? sessionId, CancellationToken cancellationToken = default)
    {
        // Use provided session ID or generate a new one
        sessionId ??= Guid.NewGuid().ToString("N")[..8];

        _logger.LogInformation("Starting bulk seeding operation {SessionId}", sessionId);

        var startTime = DateTime.UtcNow;
        var result = new SeederResult
        {
            SessionId = sessionId
        };

        try
        {
            // Validate options
            ValidateSeederOptions(options);

            // Apply defaults from configuration
            ApplyDefaultOptions(options);

            _logger.LogInformation("Seeding options: Drivers={DriversCount}, Vehicles={VehiclesCount}, BatchSize={BatchSize}, DryRun={DryRun}, Generate={GenerateData}",
                options.DriversCount, options.VehiclesCount, options.BatchSize, options.DryRun, options.GenerateData);

            if (options.DryRun)
            {
                _logger.LogInformation("DRY RUN MODE - No data will be modified");
            }

            // Send initial progress update
            await NotifyProgress(sessionId, "Starting", 0, "Initializing bulk seeding operation...");

            // Execute SQL-based seeding operation
            await ExecuteSqlSeederAsync(options, result, cancellationToken);

            result.Success = true;
            result.Duration = DateTime.UtcNow - startTime;
            result.Summary = $"Seeding completed successfully. Processed {result.ProcessedRows} rows in {result.Duration.TotalSeconds:F2} seconds.";

            _logger.LogInformation("Bulk seeding completed successfully {SessionId}. Duration: {Duration}",
                sessionId, result.Duration);

            // Send final progress update
            await NotifyProgress(sessionId, "Completed", 100, result.Summary);

            return result;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.Duration = DateTime.UtcNow - startTime;
            result.Errors.Add(ex.Message);
            result.Summary = $"Seeding failed: {ex.Message}";

            _logger.LogError(ex, "Bulk seeding failed {SessionId}", sessionId);

            // Send error progress update
            await NotifyProgress(sessionId, "Failed", 0, $"Seeding failed: {ex.Message}");

            throw;
        }
    }

    private void ValidateSeederOptions(SeederOptions options)
    {
        if (options.DriversCount.HasValue && options.DriversCount <= 0)
            throw new ArgumentException("Drivers count must be positive", nameof(options.DriversCount));

        if (options.VehiclesCount.HasValue && options.VehiclesCount <= 0)
            throw new ArgumentException("Vehicles count must be positive", nameof(options.VehiclesCount));

        if (options.BatchSize.HasValue && (options.BatchSize <= 0 || options.BatchSize > _options.MaxBatchSize))
            throw new ArgumentException($"Batch size must be between 1 and {_options.MaxBatchSize}", nameof(options.BatchSize));

        if (_options.RequireDealerSelection && string.IsNullOrWhiteSpace(options.DealerId))
            throw new ArgumentException("Dealer selection is required", nameof(options.DealerId));
    }

    private void ApplyDefaultOptions(SeederOptions options)
    {
        options.DriversCount ??= _options.DefaultDriversCount;
        options.VehiclesCount ??= _options.DefaultVehiclesCount;
        options.BatchSize ??= _options.DefaultBatchSize;

        // Apply default dealer if configured and not provided
        if (string.IsNullOrWhiteSpace(options.DealerId) && _options.DefaultDealerId.HasValue)
        {
            options.DealerId = _options.DefaultDealerId.Value.ToString();
        }
    }

    private async Task ExecuteSqlSeederAsync(SeederOptions options, SeederResult result, CancellationToken cancellationToken)
    {
        var sessionId = Guid.Parse(result.SessionId.PadRight(32, '0'));

        // Create seeding session in database
        await NotifyProgress(result.SessionId, "Running", 10, "Creating seeding session...");
        var dbSessionId = await _sqlDataService.CreateSeederSessionAsync($"BulkSeeder_{result.SessionId}", cancellationToken);

        var totalOperations = 0;
        var completedOperations = 0;

        if (options.DriversCount > 0) totalOperations++;
        if (options.VehiclesCount > 0) totalOperations++;

        try
        {
            // Generate driver data if requested
            if (options.DriversCount > 0)
            {
                await NotifyProgress(result.SessionId, "Running", 20, $"Generating {options.DriversCount} driver records...");

                var driverResult = await ExecuteWithRetry(async () =>
                    await _sqlDataService.GenerateDriverDataAsync(dbSessionId, options.DriversCount.Value, cancellationToken));

                result.ProcessedRows += driverResult.GeneratedRows;
                result.SuccessfulRows += driverResult.Success ? driverResult.GeneratedRows : 0;
                if (!driverResult.Success)
                {
                    result.Errors.AddRange(driverResult.Errors);
                }

                completedOperations++;
                var progress = 20 + (50 * completedOperations / totalOperations);
                await NotifyProgress(result.SessionId, "Running", progress, $"Driver generation completed: {driverResult.GeneratedRows} records");
            }

            // Generate vehicle data if requested
            if (options.VehiclesCount > 0)
            {
                var vehicleProgress = totalOperations == 1 ? 20 : 70;
                await NotifyProgress(result.SessionId, "Running", vehicleProgress, $"Generating {options.VehiclesCount} vehicle records...");

                var vehicleResult = await ExecuteWithRetry(async () =>
                    await _sqlDataService.GenerateVehicleDataAsync(dbSessionId, options.VehiclesCount.Value, cancellationToken));

                result.ProcessedRows += vehicleResult.GeneratedRows;
                result.SuccessfulRows += vehicleResult.Success ? vehicleResult.GeneratedRows : 0;
                if (!vehicleResult.Success)
                {
                    result.Errors.AddRange(vehicleResult.Errors);
                }

                completedOperations++;
                var progress = 20 + (50 * completedOperations / totalOperations);
                await NotifyProgress(result.SessionId, "Running", progress, $"Vehicle generation completed: {vehicleResult.GeneratedRows} records");
            }

            // Validate staged data
            await NotifyProgress(result.SessionId, "Running", 80, "Validating generated data...");
            var validationResult = await _sqlDataService.ValidateStagedDataAsync(dbSessionId, cancellationToken);

            if (!validationResult.Success)
            {
                result.Errors.AddRange(validationResult.ValidationErrors);
                throw new InvalidOperationException($"Data validation failed: {string.Join("; ", validationResult.ValidationErrors)}");
            }

            // Process staged data (unless dry run)
            if (!options.DryRun)
            {
                await NotifyProgress(result.SessionId, "Running", 90, "Processing staged data to production tables...");
                var processingResult = await _sqlDataService.ProcessStagedDataAsync(dbSessionId, options.DryRun, cancellationToken);

                if (!processingResult.Success)
                {
                    result.Errors.AddRange(processingResult.ProcessingErrors);
                    throw new InvalidOperationException($"Data processing failed: {string.Join("; ", processingResult.ProcessingErrors)}");
                }

                result.SuccessfulRows = processingResult.InsertedRows + processingResult.UpdatedRows;
                result.FailedRows = result.ProcessedRows - result.SuccessfulRows;
            }
            else
            {
                await NotifyProgress(result.SessionId, "Running", 95, "Dry run completed - no data was modified");
            }

            // Update session status
            await _sqlDataService.UpdateSeederSessionAsync(dbSessionId, "Completed",
                result.ProcessedRows, result.SuccessfulRows, result.FailedRows, cancellationToken);

        }
        catch (Exception ex)
        {
            // Update session status on failure
            try
            {
                await _sqlDataService.UpdateSeederSessionAsync(dbSessionId, "Failed",
                    result.ProcessedRows, result.SuccessfulRows, result.FailedRows, cancellationToken);
            }
            catch (Exception updateEx)
            {
                _logger.LogWarning(updateEx, "Failed to update session status on error");
            }

            throw;
        }
    }

    private async Task<T> ExecuteWithRetry<T>(Func<Task<T>> operation)
    {
        if (!_options.EnableRetry)
        {
            return await operation();
        }

        var retryPolicy = Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                retryCount: _options.MaxRetryAttempts,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(_options.RetryDelaySeconds * retryAttempt),
                onRetry: (exception, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Retry {RetryCount}/{MaxRetries} after {Delay}s: {Exception}",
                        retryCount, _options.MaxRetryAttempts, timespan.TotalSeconds, exception.Message);
                });

        return await retryPolicy.ExecuteAsync(operation);
    }

    private async Task NotifyProgress(string sessionId, string status, int progressPercentage, string message)
    {
        try
        {
            var progressUpdate = new
            {
                SessionId = sessionId,
                Status = status,
                Progress = progressPercentage,
                Message = message,
                Timestamp = DateTime.UtcNow
            };

            // Send to SignalR group (reusing existing MigrationHub infrastructure)
            await _hubContext.Clients.Group(sessionId).SendAsync("UpdateSeederProgress", progressUpdate);

            _logger.LogInformation("Seeding progress {SessionId}: {Status} ({Progress}%) - {Message}",
                sessionId, status, progressPercentage, message);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send progress notification for session {SessionId}", sessionId);
        }
    }
}
