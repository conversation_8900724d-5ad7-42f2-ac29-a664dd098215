@model XQ360.DataMigration.Web.Models.BulkSeederViewModel
@{
    ViewData["Title"] = "Data Seeder";
}

<div class="container-fluid py-4">
    <div class="row">
        <!-- Main Form Column -->
        <div class="col-lg-8">
            <!-- Import Progress Tracker (shown when import is active) -->
            @if (!string.IsNullOrEmpty(Model.ActiveSessionId))
            {
                <div class="mb-4">
                    <div id="progressTracker" class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-spinner fa-spin me-2"></i>
                                Seeding Operation in Progress
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                    role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0"
                                    aria-valuemax="100">
                                    0%
                                </div>
                            </div>
                            <div id="progressStatus" class="text-muted">Initializing...</div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="cancelSeeding()">
                                    <i class="fas fa-stop me-1"></i>
                                    Cancel Operation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Simplified Seeder Form -->
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        Bulk Data Seeder Configuration
                    </h4>
                </div>

                <div class="card-body">
                    <form id="seederForm" asp-action="CreateSession" asp-controller="Seeder" method="post">
                        <!-- Environment Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-server me-2"></i>
                                Environment
                            </label>
                            <select asp-for="SelectedEnvironment" class="form-select" id="environmentSelect" required>
                                <option value="">Select an environment...</option>
                                @foreach (var env in Model.AvailableEnvironments)
                                {
                                    <option value="@env.Key" data-description="@env.Description"
                                        selected="@(env.Key == Model.SelectedEnvironment)">
                                        @env.DisplayName
                                    </option>
                                }
                            </select>
                            <div class="form-text">
                                <strong>Current:</strong> @Model.CurrentEnvironment
                            </div>
                        </div>

                        <!-- Dealer Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-building me-2"></i>
                                Dealer
                            </label>
                            <div id="dealer-selection-container" style="position: relative;">
                                <input type="text" id="dealer-input" class="form-control" autocomplete="off"
                                    placeholder="Type to search dealers..." required>
                                <div id="selected-dealer-display" class="mt-2" style="display: none;">
                                    <div
                                        class="alert alert-success compact-selected-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="selected-dealer-name"></strong>
                                            <div class="small text-muted compact-subdomain"
                                                id="selected-dealer-subdomain"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="clearDealer()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selected-dealer-id" name="SelectedDealer.Id"
                                value="@Model.SelectedDealer?.Id" />
                            <input type="hidden" id="selected-dealer-name-hidden" name="SelectedDealer.Name"
                                value="@Model.SelectedDealer?.Name" />
                        </div>

                        <!-- Customer Selection -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-users me-2"></i>
                                Customer
                            </label>
                            <div id="customer-selection-container" style="position: relative;">
                                <input type="text" id="customer-input" name="CustomerInput" class="form-control"
                                    autocomplete="off"
                                    placeholder="Select a dealer first, then type to search customers..." disabled>
                                <div id="selected-customer-display" class="mt-2" style="display: none;">
                                    <div
                                        class="alert alert-success compact-selected-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="selected-customer-name"></strong>
                                            <div class="small text-muted compact-contact"
                                                id="selected-customer-contact"></div>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="clearCustomer()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selected-customer-id" name="SelectedCustomer.Id"
                                value="@Model.SelectedCustomer?.Id" />
                            <input type="hidden" id="selected-customer-name-hidden" name="SelectedCustomer.Name"
                                value="@Model.SelectedCustomer?.Name" />
                        </div>

                        <!-- Vehicle Count -->
                        <div class="mb-4">
                            <label for="vehicleCount" class="form-label">
                                <i class="fas fa-car me-2"></i>
                                Vehicle Count
                            </label>
                            <input type="number" id="vehicleCount" name="VehicleCount" class="form-control"
                                value="@(Model.VehicleCount ?? 5000)" min="1" max="100000"
                                placeholder="Enter vehicle count" required>
                            <div class="form-text">Enter the number of vehicles to generate</div>
                        </div>

                        <!-- Driver Count -->
                        <div class="mb-4">
                            <label for="driverCount" class="form-label">
                                <i class="fas fa-id-card me-2"></i>
                                Driver Count
                            </label>
                            <input type="number" id="driverCount" name="DriverCount" class="form-control"
                                value="@(Model.DriverCount ?? 3000)" min="1" max="200000"
                                placeholder="Enter driver count" required>
                            <div class="form-text">Enter the number of drivers to generate</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary" id="startSeedingBtn">
                                <i class="fas fa-play me-1"></i>
                                Start Seeding Operation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Configuration Summary Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 1rem;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-ul me-2"></i>
                            Configuration Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="summary-environment" class="mb-3">
                            <h6>Environment</h6>
                            <div class="text-muted">@Model.CurrentEnvironment</div>
                        </div>

                        <div id="summary-dealer" class="mb-3">
                            <h6>Dealer</h6>
                            <div class="text-muted">Not selected</div>
                        </div>

                        <div id="summary-customer" class="mb-3">
                            <h6>Customer</h6>
                            <div class="text-muted">Not selected</div>
                        </div>

                        <div id="summary-counts" class="mb-3">
                            <h6>Data to Generate</h6>
                            <div class="text-muted">
                                <div id="summary-vehicles">0 vehicles</div>
                                <div id="summary-drivers">0 drivers</div>
                            </div>
                        </div>

                        <div id="summary-estimated-time" class="mb-3">
                            <h6>Estimated Time</h6>
                            <div class="text-muted" id="estimated-time-display">
                                <i class="fas fa-clock me-1"></i>
                                <span id="estimated-time-text">Select data counts to see estimate</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            initializeSeederForm();
        });

        function initializeSeederForm() {
            // Initialize dropdown functionality
            initializeDealerDropdown();

            // Initialize with existing selections if available
            @if (Model.SelectedDealer != null)
                {
                    <text>selectDealer(@Html.Raw(Json.Serialize(Model.SelectedDealer.Id)), @Html.Raw(Json.Serialize(Model.SelectedDealer.Name)), @Html.Raw(Json.Serialize(Model.SelectedDealer.Subdomain)));</text>
            }
                @if (Model.SelectedCustomer != null)
                {
                    <text>selectCustomer(@Html.Raw(Json.Serialize(Model.SelectedCustomer.Id)), @Html.Raw(Json.Serialize(Model.SelectedCustomer.Name)), @Html.Raw(Json.Serialize(Model.SelectedCustomer.ContactName ?? "")), @Html.Raw(Json.Serialize(Model.SelectedCustomer.ContactEmail ?? "")));</text>
            }

                // Handle form input changes for summary updates
                $('#dealer-input, #customer-input, #vehicleCount, #driverCount').on('input change', updateSummary);
            updateSummary();
        }

        function selectDealer(id, name, subdomain) {
            $('#selected-dealer-id').val(id);
            $('#selected-dealer-name-hidden').val(name);
            $('#selected-dealer-name').text(name);
            $('#selected-dealer-subdomain').text(subdomain);
            $('#dealer-input').hide();
            $('#selected-dealer-display').show();

            // Enable customer input and update placeholder
            $('#customer-input').prop('disabled', false).attr('placeholder', 'Type to search customers...');

            updateSummary();
        }

        function clearDealer() {
            $('#selected-dealer-id').val('');
            $('#selected-dealer-name-hidden').val('');
            $('#dealer-input').val('').show();
            $('#selected-dealer-display').hide();
            $('#dealer-dropdown').hide(); // Hide dealer dropdown

            // Disable customer input and reset placeholder
            $('#customer-input').prop('disabled', true).attr('placeholder', 'Select a dealer first, then type to search customers...');

            clearCustomer(); // Clear customer when dealer changes
            $('#customer-dropdown').hide(); // Hide customer dropdown
            updateSummary();
        }

        function selectCustomer(id, name, contactName, contactEmail) {
            $('#selected-customer-id').val(id);
            $('#selected-customer-name-hidden').val(name);
            $('#selected-customer-name').text(name);

            let contactInfo = contactName || '';
            if (contactEmail) {
                contactInfo += contactInfo ? ' (' + contactEmail + ')' : contactEmail;
            }
            $('#selected-customer-contact').text(contactInfo || 'No contact information');

            // Ensure customer input field has proper value and is enabled for form submission
            $('#customer-input').val(name).prop('disabled', false).hide();
            $('#selected-customer-display').show();
            updateSummary();
        }

        function clearCustomer() {
            $('#selected-customer-id').val('');
            $('#selected-customer-name-hidden').val('');

            // Reset customer input state based on dealer selection
            const dealerId = $('#selected-dealer-id').val();
            const customerInput = $('#customer-input');

            customerInput.val('').show();
            if (dealerId) {
                customerInput.prop('disabled', false).attr('placeholder', 'Type to search customers...');
            } else {
                customerInput.prop('disabled', true).attr('placeholder', 'Select a dealer first, then type to search customers...');
            }

            $('#selected-customer-display').hide();
            updateSummary();
        }

        function updateSummary() {
            // Update dealer summary
            const dealerName = $('#selected-dealer-name').text();
            $('#summary-dealer .text-muted').text(dealerName || 'Not selected');

            // Update customer summary
            const customerName = $('#selected-customer-name').text();
            $('#summary-customer .text-muted').text(customerName || 'Not selected');

            // Update counts summary
            const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
            const driverCount = parseInt($('#driverCount').val()) || 0;
            $('#summary-vehicles').text(vehicleCount.toLocaleString() + ' vehicles');
            $('#summary-drivers').text(driverCount.toLocaleString() + ' drivers');

            // Update estimated time
            updateEstimatedTime(vehicleCount, driverCount);
        }

        function updateEstimatedTime(vehicleCount, driverCount) {
            if (vehicleCount <= 0 && driverCount <= 0) {
                $('#estimated-time-text').text('Select data counts to see estimate');
                return;
            }

            // Estimation based on performance benchmarks:
            // - Driver creation: ~5,000/minute (optimized)
            // - Vehicle creation: ~2,000/minute (optimized)
            // - Additional overhead for database operations and API calls

            const driverTimeMinutes = driverCount > 0 ? Math.max(driverCount / 5000, 0.1) : 0;
            const vehicleTimeMinutes = vehicleCount > 0 ? Math.max(vehicleCount / 2000, 0.1) : 0;

            // Add overhead for initialization, validation, and cleanup (minimum 30 seconds)
            const overheadMinutes = 0.5;

            const totalMinutes = driverTimeMinutes + vehicleTimeMinutes + overheadMinutes;

            // Format time display
            let timeText;
            if (totalMinutes < 1) {
                timeText = '< 1 minute';
            } else if (totalMinutes < 60) {
                timeText = `${Math.ceil(totalMinutes)} minute${Math.ceil(totalMinutes) !== 1 ? 's' : ''}`;
            } else {
                const hours = Math.floor(totalMinutes / 60);
                const minutes = Math.ceil(totalMinutes % 60);
                if (minutes === 0) {
                    timeText = `${hours} hour${hours !== 1 ? 's' : ''}`;
                } else {
                    timeText = `${hours} hour${hours !== 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''}`;
                }
            }

            $('#estimated-time-text').text(timeText);
        }

        // Dealer dropdown functionality
        function initializeDealerDropdown() {
            let dealerSearchTimeout;

            $('#dealer-input').on('input', function () {
                const query = $(this).val().trim();

                // Clear previous timeout
                if (dealerSearchTimeout) {
                    clearTimeout(dealerSearchTimeout);
                }

                // Hide dropdown if query is empty
                if (query.length < 2) {
                    $('#dealer-dropdown').hide();
                    return;
                }

                // Debounce search
                dealerSearchTimeout = setTimeout(() => {
                    searchDealers(query);
                }, 300);
            });

            // Customer search functionality
            let customerSearchTimeout;
            $('#customer-input').on('input', function () {
                const query = $(this).val().trim();
                const dealerId = $('#selected-dealer-id').val();

                // Clear previous timeout
                if (customerSearchTimeout) {
                    clearTimeout(customerSearchTimeout);
                }

                // Hide dropdown if query is empty or no dealer selected
                if (query.length < 2 || !dealerId) {
                    $('#customer-dropdown').hide();
                    return;
                }

                // Debounce search
                customerSearchTimeout = setTimeout(() => {
                    searchCustomers(dealerId, query);
                }, 300);
            });

            // Hide dropdown when clicking outside
            $(document).on('click', function (e) {
                if (!$(e.target).closest('#dealer-selection-container').length) {
                    $('#dealer-dropdown').hide();
                }
                if (!$(e.target).closest('#customer-selection-container').length) {
                    $('#customer-dropdown').hide();
                }
            });
        }

        function searchDealers(query) {
            $.ajax({
                url: '/api/dealers',
                method: 'GET',
                data: {
                    query: query,
                    pageSize: 10,
                    activeOnly: true
                },
                success: function (response) {
                    displayDealerResults(response.dealers);
                },
                error: function (xhr, status, error) {
                    console.error('Error searching dealers:', error);
                    $('#dealer-dropdown').hide();
                }
            });
        }

        function displayDealerResults(dealers) {
            if (!dealers || dealers.length === 0) {
                $('#dealer-dropdown').hide();
                return;
            }

            let dropdown = $('#dealer-dropdown');
            if (dropdown.length === 0) {
                dropdown = $('<div id="dealer-dropdown" class="dropdown-menu show" style="position: absolute; z-index: 1000; max-height: 200px; overflow-y: auto;"></div>');
                $('#dealer-selection-container').append(dropdown);
            }

            dropdown.empty();

            dealers.forEach(dealer => {
                const item = $(`
                                                                                                                            <a class="dropdown-item" href="#" data-dealer-id="${dealer.id}" data-dealer-name="${dealer.name}" data-dealer-subdomain="${dealer.subdomain}">
                                                                                                                                <div>
                                                                                                                                    <strong>${dealer.name}</strong>
                                                                                                                                    ${dealer.subdomain ? `<div class="small text-muted">${dealer.subdomain}</div>` : ''}
                                                                                                                                </div>
                                                                                                                            </a>
                                                                                                                        `);

                item.on('click', function (e) {
                    e.preventDefault();
                    const dealerId = $(this).data('dealer-id');
                    const dealerName = $(this).data('dealer-name');
                    const dealerSubdomain = $(this).data('dealer-subdomain');

                    selectDealer(dealerId, dealerName, dealerSubdomain);
                    dropdown.hide();

                    // Load customers for selected dealer
                    loadCustomersForDealer(dealerId);
                });

                dropdown.append(item);
            });

            dropdown.show();
        }

        // Customer dropdown functionality
        function loadCustomersForDealer(dealerId) {
            if (!dealerId) {
                $('#customer-dropdown').hide();
                return;
            }

            $.ajax({
                url: '/api/customers',
                method: 'GET',
                data: {
                    dealerId: dealerId,
                    pageSize: 100,
                    activeOnly: true
                },
                success: function (response) {
                    populateCustomerDropdown(response.customers);
                },
                error: function (xhr, status, error) {
                    console.error('Error loading customers:', error);
                    $('#customer-dropdown').hide();
                }
            });
        }

        function populateCustomerDropdown(customers) {
            let dropdown = $('#customer-dropdown');
            if (dropdown.length === 0) {
                dropdown = $('<div id="customer-dropdown" class="dropdown-menu show" style="position: absolute; z-index: 1000; max-height: 200px; overflow-y: auto;"></div>');
                $('#customer-selection-container').append(dropdown);
            }

            dropdown.empty();

            if (!customers || customers.length === 0) {
                dropdown.append('<div class="dropdown-item-text text-muted">No customers found for this dealer</div>');
                dropdown.show();
                return;
            }

            customers.forEach(customer => {
                const item = $(`
                                                                                                                            <a class="dropdown-item" href="#" data-customer-id="${customer.id}" data-customer-name="${customer.name}" data-customer-contact-name="${customer.contactName || ''}" data-customer-contact-email="${customer.contactEmail || ''}">
                                                                                                                                <div>
                                                                                                                                    <strong>${customer.name}</strong>
                                                                                                                                    ${customer.contactName || customer.contactEmail ? `<div class="small text-muted">${customer.contactName || ''} ${customer.contactEmail || ''}</div>` : ''}
                                                                                                                                </div>
                                                                                                                            </a>
                                                                                                                        `);

                item.on('click', function (e) {
                    e.preventDefault();
                    const customerId = $(this).data('customer-id');
                    const customerName = $(this).data('customer-name');
                    const contactName = $(this).data('customer-contact-name');
                    const contactEmail = $(this).data('customer-contact-email');

                    selectCustomer(customerId, customerName, contactName, contactEmail);
                    dropdown.hide();
                });

                dropdown.append(item);
            });

            dropdown.show();
        }

        function searchCustomers(dealerId, query) {
            $.ajax({
                url: '/api/customers',
                method: 'GET',
                data: {
                    dealerId: dealerId,
                    query: query,
                    pageSize: 20,
                    activeOnly: true
                },
                success: function (response) {
                    populateCustomerDropdown(response.customers);
                },
                error: function (xhr, status, error) {
                    console.error('Error searching customers:', error);
                    $('#customer-dropdown').hide();
                }
            });
        }

        function validateForm(showSuccessAlert = true) {
            const errors = [];

            if (!$('#selected-dealer-id').val()) {
                errors.push('Please select a dealer');
            }

            // Only require customer if dealer is selected
            const dealerId = $('#selected-dealer-id').val();
            if (dealerId && !$('#selected-customer-id').val()) {
                errors.push('Please select a customer');
            }

            const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
            if (vehicleCount <= 0) {
                errors.push('Vehicle count must be greater than 0');
            }

            const driverCount = parseInt($('#driverCount').val()) || 0;
            if (driverCount <= 0) {
                errors.push('Driver count must be greater than 0');
            }

            if (errors.length > 0) {
                alert('Validation errors:\n\n' + errors.join('\n'));
                return false;
            }

            if (showSuccessAlert) {
                alert('Configuration is valid!');
            }
            return true;
        }

        function cancelSeeding() {
            if (confirm('Are you sure you want to cancel the seeding operation?')) {
                const sessionId = window.currentSessionId;
                if (sessionId) {
                    $.ajax({
                        url: `/api/bulk-seeder/cancel/${sessionId}`,
                        method: 'POST',
                        success: function (response) {
                            console.log('Seeding operation cancelled:', response);
                            $('#progressTracker').hide();
                            alert('Seeding operation cancelled successfully');
                        },
                        error: function (xhr, status, error) {
                            console.error('Error cancelling seeding operation:', error);
                            $('#progressTracker').hide();
                            alert('Failed to cancel seeding operation, but hiding progress tracker');
                        }
                    });
                } else {
                    $('#progressTracker').hide();
                    alert('No active seeding session to cancel');
                }
            }
        }

        // Form submission handler
        $('#seederForm').on('submit', function (e) {
            e.preventDefault();

            // Ensure customer input is enabled if dealer is selected and customer input has value
            const dealerId = $('#selected-dealer-id').val();
            const customerInput = $('#customer-input');
            const customerValue = customerInput.val().trim();

            if (dealerId && customerValue && customerInput.prop('disabled')) {
                customerInput.prop('disabled', false);
            }

            // Validate form without showing success alert
            if (!validateForm(false)) {
                return false;
            }

            // Prepare data for API call
            const customerId = $('#selected-customer-id').val();
            const vehicleCount = parseInt($('#vehicleCount').val()) || 0;
            const driverCount = parseInt($('#driverCount').val()) || 0;

            // Ensure we have dealer and customer IDs
            if (!dealerId) {
                alert('Please select a dealer before starting the seeding operation.');
                return false;
            }

            if (!customerId) {
                alert('Please select a customer before starting the seeding operation.');
                return false;
            }

            // Disable the submit button and show loading state
            const submitBtn = $('#startSeedingBtn');
            window.originalButtonText = submitBtn.html(); // Store original text globally
            window.isSeederOperationActive = true; // Track operation state
            updateSeederButtonDisplay(0, 'Starting', 'Initializing seeding operation...');

            // Call the API to start seeding
            startSeedingOperation(dealerId, customerId, vehicleCount, driverCount);
            // Note: Button will be re-enabled by SignalR completion/failure handlers
        });

        // Function to start seeding operation via API
        function startSeedingOperation(dealerId, customerId, vehicleCount, driverCount) {
            const requestData = {
                DealerId: dealerId,
                CustomerId: customerId,
                VehicleCount: vehicleCount,
                DriverCount: driverCount
            };

            return $.ajax({
                url: '/api/bulk-seeder/start',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData),
                success: function (response) {
                    console.log('Seeding operation started:', response);

                    // Store the session ID for SignalR group joining
                    window.currentSessionId = response.sessionId;

                    // Show success message
                    alert(`Seeding operation started successfully!\n\nSession ID: ${response.sessionId}\nStatus: ${response.status}\n\n${response.message}`);

                    // Show progress tracker
                    $('#progressTracker').show();
                    $('#progressStatus').text('Seeding operation in progress...');

                    // Initialize SignalR connection and join the session group
                    initializeSignalRForSeeding(response.sessionId);
                },
                error: function (xhr, status, error) {
                    console.error('Error starting seeding operation:', error);

                    let errorMessage = 'Failed to start seeding operation.';
                    if (xhr.responseJSON && xhr.responseJSON.detail) {
                        errorMessage += '\n\nError: ' + xhr.responseJSON.detail;
                    } else if (xhr.responseText) {
                        errorMessage += '\n\nError: ' + xhr.responseText;
                    }

                    alert(errorMessage);
                }
            });
        }

        // Initialize SignalR connection for real-time seeding progress updates
        let seederConnection = null;

        function initializeSignalRForSeeding(sessionId) {
            if (seederConnection) {
                seederConnection.stop();
            }

            seederConnection = new signalR.HubConnectionBuilder()
                .withUrl("/migrationHub")
                .build();

            // Listen for seeding progress updates
            seederConnection.on("UpdateSeederProgress", function (progressUpdate) {
                console.log("Seeding progress update received:", progressUpdate);
                updateSeederProgressDisplay(progressUpdate);
            });

            // Listen for session completion
            seederConnection.on("SeederSessionCompleted", function (sessionInfo) {
                console.log("✅ Seeding session completed:", sessionInfo);
                $('#progressStatus').text(`Seeding completed! ${sessionInfo.Summary || ''}`);
                $('#progressBar').css('width', '100%').attr('aria-valuenow', 100).text('100%');

                // Update button to show completion state
                updateSeederButtonDisplay(100, 'Completed', 'Seeding Complete');

                console.log("🔄 Button updated to completion state");

                // After a brief moment, restore the button to its original state
                setTimeout(() => {
                    const submitBtn = $('#startSeedingBtn');
                    window.isSeederOperationActive = false;
                    submitBtn.prop('disabled', false)
                        .html(window.originalButtonText || '<i class="fas fa-play me-1"></i>Start Seeding Operation')
                        .attr('class', 'btn btn-primary')
                        .css('background-image', 'none');
                    console.log("🔄 Button restored to original state");
                }, 3000);

                setTimeout(() => {
                    $('#progressTracker').hide();
                    console.log("📊 Progress tracker hidden");
                }, 5000);
            });

            // Listen for session failures
            seederConnection.on("SeederSessionFailed", function (sessionInfo) {
                console.log("Seeding session failed:", sessionInfo);
                $('#progressStatus').text(`Seeding failed: ${sessionInfo.Summary || 'Unknown error'}`);
                $('#progressBar').removeClass('progress-bar-striped progress-bar-animated').addClass('bg-danger');

                // Update button to show failure state
                updateSeederButtonDisplay(0, 'Failed', 'Seeding Failed');

                console.log("🔄 Button updated to failure state");

                // After a brief moment, restore the button to its original state
                setTimeout(() => {
                    const submitBtn = $('#startSeedingBtn');
                    window.isSeederOperationActive = false;
                    submitBtn.prop('disabled', false)
                        .html(window.originalButtonText || '<i class="fas fa-play me-1"></i>Start Seeding Operation')
                        .attr('class', 'btn btn-primary')
                        .css('background-image', 'none');
                    console.log("🔄 Button restored to original state after failure");
                }, 3000);

                setTimeout(() => {
                    $('#progressTracker').hide();
                }, 10000);
            });

            // Start connection and join the session group
            seederConnection.start().then(function () {
                console.log("🔗 SignalR connected for seeding session:", sessionId);
                // Join the specific session group to receive updates
                return seederConnection.invoke("JoinMigrationGroup", sessionId);
            }).then(function () {
                console.log("👥 Joined seeding session group:", sessionId);
                console.log("🎯 Listening for messages on group:", sessionId);
            }).catch(function (err) {
                console.error("❌ SignalR connection error:", err);
                // Fallback to simulated progress if SignalR fails
                simulateProgress();
            });
        }

        function updateSeederProgressDisplay(progressUpdate) {
            const progress = progressUpdate.Progress || 0;
            const status = progressUpdate.Status || 'Running';
            const message = progressUpdate.Message || 'Processing...';

            console.log(`Updating seeder progress: ${progress}% - ${message}`);

            // Update progress bar
            $('#progressBar').css('width', progress + '%').attr('aria-valuenow', progress).text(progress + '%');

            // Update status message
            $('#progressStatus').text(message);

            // Update button text with current status if operation is active
            if (window.isSeederOperationActive) {
                updateSeederButtonDisplay(progress, status, message);
            }

            // Update bar color based on status
            if (status === 'Completed') {
                $('#progressBar').removeClass('progress-bar-striped progress-bar-animated').addClass('bg-success');
            } else if (status === 'Failed') {
                $('#progressBar').removeClass('progress-bar-striped progress-bar-animated').addClass('bg-danger');
            }
        }

        function updateSeederButtonDisplay(progress, status, message) {
            const submitBtn = $('#startSeedingBtn');
            let buttonText = '';
            let iconClass = '';
            let buttonClass = 'btn btn-primary';

            // Determine button appearance based on status and progress
            switch (status.toLowerCase()) {
                case 'running':
                case 'in progress':
                case 'processing':
                    iconClass = 'fas fa-spinner fa-spin';
                    if (progress > 0) {
                        buttonText = `Seeding... ${progress}%`;
                        // Add visual progress indication
                        if (progress < 25) {
                            buttonClass = 'btn btn-info';
                        } else if (progress < 75) {
                            buttonClass = 'btn btn-warning';
                        } else {
                            buttonClass = 'btn btn-success';
                        }
                    } else {
                        buttonText = 'Starting Seeding...';
                        buttonClass = 'btn btn-info';
                    }
                    break;
                case 'completed':
                case 'complete':
                    iconClass = 'fas fa-check';
                    buttonText = 'Seeding Complete';
                    buttonClass = 'btn btn-success';
                    break;
                case 'failed':
                case 'error':
                    iconClass = 'fas fa-exclamation-triangle';
                    buttonText = 'Seeding Failed';
                    buttonClass = 'btn btn-danger';
                    break;
                default:
                    iconClass = 'fas fa-spinner fa-spin';
                    buttonText = message || 'Processing...';
                    buttonClass = 'btn btn-secondary';
            }

            // Update button with new text, icon, and styling
            submitBtn.html(`<i class="${iconClass} me-1"></i>${buttonText}`)
                .attr('class', buttonClass)
                .prop('disabled', true);

            // Add a subtle progress indicator to the button if we have progress
            if (progress > 0 && status.toLowerCase() !== 'completed' && status.toLowerCase() !== 'failed') {
                // Create a subtle background gradient to show progress
                const progressGradient = `linear-gradient(to right, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.2) ${progress}%, transparent ${progress}%, transparent 100%)`;
                submitBtn.css('background-image', progressGradient);
            } else {
                submitBtn.css('background-image', 'none');
            }
        }

        // Simulate progress for demo purposes (fallback only)
        function simulateProgress() {
            console.log("Using simulated progress as fallback");
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    $('#progressStatus').text('Seeding operation completed!');
                    setTimeout(() => {
                        $('#progressTracker').hide();
                    }, 3000);
                }

                $('#progressBar').css('width', progress + '%').attr('aria-valuenow', progress).text(Math.round(progress) + '%');
            }, 1000);
        }

        // Test function to simulate specific progress values for testing
        function testSeederProgress(progressValue, status = 'Running', message = null) {
            console.log(`🧪 Testing seeder progress at ${progressValue}%`);

            // Set the operation as active for testing
            window.isSeederOperationActive = true;

            // Create a mock progress update object
            const progressUpdate = {
                Progress: progressValue,
                Status: status,
                Message: message || `Processing seeding operation... ${progressValue}%`,
                SessionId: 'test-session-' + Date.now(),
                Timestamp: new Date().toISOString()
            };

            // Call the progress update function
            updateSeederProgressDisplay(progressUpdate);

            // Log the current button state for verification
            const submitBtn = $('#startSeedingBtn');
            console.log('🔍 Button state verification:');
            console.log('  - Text:', submitBtn.text());
            console.log('  - HTML:', submitBtn.html());
            console.log('  - Classes:', submitBtn.attr('class'));
            console.log('  - Disabled:', submitBtn.prop('disabled'));
            console.log('  - Background Image:', submitBtn.css('background-image'));

            return {
                progress: progressValue,
                buttonText: submitBtn.text(),
                buttonHtml: submitBtn.html(),
                buttonClasses: submitBtn.attr('class'),
                isDisabled: submitBtn.prop('disabled'),
                backgroundImage: submitBtn.css('background-image')
            };
        }

        // Test function specifically for 80% progress
        function test80PercentProgress() {
            console.log('🎯 Testing 80% progress scenario...');

            const result = testSeederProgress(80, 'Running', 'Seeding vehicles and drivers... 80% complete');

            // Verify all requirements
            console.log('✅ Verification Results:');
            console.log('1. Button text shows "Seeding... 80%":', result.buttonText.includes('Seeding... 80%'));
            console.log('2. Button has success color (green):', result.buttonClasses.includes('btn-success'));
            console.log('3. Button is disabled:', result.isDisabled === true);
            console.log('4. Has spinner icon:', result.buttonHtml.includes('fa-spinner fa-spin'));
            console.log('5. Has background gradient:', result.backgroundImage !== 'none' && result.backgroundImage.includes('linear-gradient'));

            return result;
        }

        // Test function for complete progress sequence
        function testProgressSequence() {
            console.log('🔄 Testing complete progress sequence...');

            const progressValues = [0, 15, 30, 50, 80, 95, 100];
            let index = 0;

            const interval = setInterval(() => {
                if (index >= progressValues.length) {
                    clearInterval(interval);
                    console.log('✅ Progress sequence test completed');
                    return;
                }

                const progress = progressValues[index];
                const status = progress === 100 ? 'Completed' : 'Running';
                testSeederProgress(progress, status);
                index++;
            }, 2000);
        }

        // Make test functions globally available for console testing
        window.testSeederProgress = testSeederProgress;
        window.test80PercentProgress = test80PercentProgress;
        window.testProgressSequence = testProgressSequence;
    </script>
}
